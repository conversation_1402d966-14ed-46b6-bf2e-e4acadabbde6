body {
  font-family: 'Raleway', 'Helvetica', sans-serif;
  font-size: 11px;
  background: #fff;
  color: #222222;
  overflow: hidden;
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: 100%;
}

html {
  width: 100%;
  height: 100%;
  font-size: 62.5%;
}

* {
  -webkit-touch-callout: none;                /* prevent callout to copy image, etc when tap to hold */
  -webkit-text-size-adjust: none;             /* prevent webkit from resizing text to fit */
  -webkit-tap-highlight-color: rgba(0,0,0,0); /* prevent tap highlight color / shadow */
  -webkit-user-select: none;                  /* prevent copy paste, to allow, change 'none' to 'text' */
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.info-container {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 1.5rem;
}

.main-title {
  position: absolute;
  top: 0;
  left: 0;
  padding: 1.5rem;
  font-size: 12px;
}

.info {
  font-weight: 700;
}

.code {
  font-weight: 300;  
}

p {
  line-height: 0.6rem;
}

a, a:active, a:visited {
  color: #2b84e0;
  text-decoration: none;
  position: relative;
}
a::after {
  position: absolute;
  content: ' ';
  display: block;
  width: 100%;
  height: 2px;
  bottom: 0px;
  opacity: 0;
  left: 0;
  background: currentColor;
  -webkit-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  -moz-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  -o-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
}
a:hover::after {
  height: 2px;
  opacity: 1;
  bottom: -3px;
  -webkit-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  -moz-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  -o-transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
  transition: all 0.25s cubic-bezier(0.190, 1.000, 0.220, 1.000);
}